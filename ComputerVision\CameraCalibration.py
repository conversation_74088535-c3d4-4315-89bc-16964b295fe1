import cv2
import numpy as np
import glob

# Chessboard thông tin
chessboard_size = (10, 7)  # OpenCV sample là (9,6) corners

# Tạo điểm 3D chuẩn
objp = np.zeros((np.prod(chessboard_size), 3), np.float32)
objp[:, :2] = np.indices(chessboard_size).T.reshape(-1, 2)

objpoints = []  # 3D points
imgpoints = []  # 2D points

# Load ảnh calibration
images = glob.glob('D:/AI/CameraCalib/frame_and/frame*.jpg')  # thư mục ảnh

for fname in images:
    img = cv2.imread(fname)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    ret, corners = cv2.findChessboardCorners(gray, chessboard_size, None)

    if ret:
        objpoints.append(objp)
        corners2 = cv2.cornerSubPix(gray, corners, (55,55), (-1,-1),
                    (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
        imgpoints.append(corners2)

# Calibration
ret, mtx, dist, rvecs, tvecs = cv2.calibrateCamera(objpoints, imgpoints, gray.shape[::-1], None, None)

print("Camera Matrix:\n", mtx)
print("Distortion Coefficients:\n", dist)

# --- UNDISTORT ---
# Chọn một ảnh test
img = cv2.imread('calib_images_gray/gray_20250603_102142.jpg')  # lấy ảnh khác trong tập

h, w = img.shape[:2]

# Undistort ảnh
newcameramtx, roi = cv2.getOptimalNewCameraMatrix(mtx, dist, (w,h), 1, (w,h))
undistorted_img = cv2.undistort(img, mtx, dist, None, newcameramtx)

# Crop vùng hợp lệ (optional)
x, y, w, h = roi
undistorted_img = undistorted_img[y:y+h, x:x+w]

# Hiển thị ảnh gốc và ảnh đã undistort
cv2.imshow('Original Image', img)
cv2.imshow('Undistorted Image', undistorted_img)
cv2.waitKey(0)
cv2.destroyAllWindows()
