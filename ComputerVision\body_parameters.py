"""
<PERSON><PERSON><PERSON> <PERSON>ộ thông số cơ thể khác nhau để tối ưu hóa distortion coefficients
"""

# Thông số mặc định
DEFAULT_PARAMS = {
    'head_height_unit': 1.0,
    'body_height_factor': 8.0,
    'shoulder_width_factor': 1.0,
    'hip_width_factor_male': 0.75,
    'arm_length_factor': 3.0,
    'leg_length_factor': 4.0,
    'elbow_outward_factor': 1.0,
    'wrist_outward_factor': 1.5,
    'limb_depth_factor': 0.1,
    'knee_height_ratio': 0.5
}

# Thông số điều chỉnh để có distortion coefficients gần với mục tiêu
# [[ 1.07834685e-01  1.11976029e-01 -1.44669794e-03 -1.83572338e-04 -1.34815082e+00]]
TARGET_DISTORTION_PARAMS_V1 = {
    'head_height_unit': 0.9,
    'body_height_factor': 8.2,
    'shoulder_width_factor': 1.1,
    'hip_width_factor_male': 0.8,
    'arm_length_factor': 2.9,
    'leg_length_factor': 4.1,
    'elbow_outward_factor': 1.6,
    'wrist_outward_factor': 2.0,
    'limb_depth_factor': 0.08,
    'knee_height_ratio': 0.48
}

# Thông số tối ưu được tìm ra bởi hệ thống tự động (Error: 34.18)
# Achieved distortion: [13.129, -25.005, -1.3927, 0.0082962, 17.791]
OPTIMIZED_TARGET_PARAMS = {
    'head_height_unit': 0.95,
    'body_height_factor': 7.9,
    'shoulder_width_factor': 0.95,
    'hip_width_factor_male': 0.75,
    'arm_length_factor': 3.0,
    'leg_length_factor': 4.0,
    'elbow_outward_factor': 1.4,
    'wrist_outward_factor': 1.7,
    'limb_depth_factor': 0.1,
    'knee_height_ratio': 0.5
}

# Thông số điều chỉnh khác (có thể thử nghiệm)
TARGET_DISTORTION_PARAMS_V2 = {
    'head_height_unit': 0.85,
    'body_height_factor': 8.3,
    'shoulder_width_factor': 1.15,
    'hip_width_factor_male': 0.82,
    'arm_length_factor': 2.85,
    'leg_length_factor': 4.05,
    'elbow_outward_factor': 1.65,
    'wrist_outward_factor': 2.1,
    'limb_depth_factor': 0.075,
    'knee_height_ratio': 0.47
}

# Thông số cho distortion coefficients thấp hơn
LOW_DISTORTION_PARAMS = {
    'head_height_unit': 1.1,
    'body_height_factor': 7.8,
    'shoulder_width_factor': 0.95,
    'hip_width_factor_male': 0.7,
    'arm_length_factor': 3.1,
    'leg_length_factor': 3.9,
    'elbow_outward_factor': 0.9,
    'wrist_outward_factor': 1.4,
    'limb_depth_factor': 0.12,
    'knee_height_ratio': 0.52
}

# Thông số cho distortion coefficients cao hơn
HIGH_DISTORTION_PARAMS = {
    'head_height_unit': 0.8,
    'body_height_factor': 8.5,
    'shoulder_width_factor': 1.25,
    'hip_width_factor_male': 0.85,
    'arm_length_factor': 2.7,
    'leg_length_factor': 4.3,
    'elbow_outward_factor': 1.8,
    'wrist_outward_factor': 2.3,
    'limb_depth_factor': 0.06,
    'knee_height_ratio': 0.43
}

def get_parameter_set(name):
    """
    Lấy bộ thông số theo tên

    Args:
        name (str): Tên bộ thông số ('default', 'target_v1', 'target_v2', 'optimized', 'low_distortion', 'high_distortion')

    Returns:
        dict: Bộ thông số tương ứng
    """
    params_dict = {
        'default': DEFAULT_PARAMS,
        'target_v1': TARGET_DISTORTION_PARAMS_V1,
        'target_v2': TARGET_DISTORTION_PARAMS_V2,
        'optimized': OPTIMIZED_TARGET_PARAMS,
        'low_distortion': LOW_DISTORTION_PARAMS,
        'high_distortion': HIGH_DISTORTION_PARAMS
    }

    return params_dict.get(name, DEFAULT_PARAMS)

def list_available_parameter_sets():
    """
    Liệt kê tất cả các bộ thông số có sẵn
    """
    return ['default', 'target_v1', 'target_v2', 'optimized', 'low_distortion', 'high_distortion']

def print_parameter_info():
    """
    In thông tin về các bộ thông số
    """
    print("Các bộ thông số có sẵn:")
    print("1. default: Thông số mặc định")
    print("2. target_v1: Tối ưu cho distortion coefficients mục tiêu (phiên bản 1)")
    print("3. target_v2: Tối ưu cho distortion coefficients mục tiêu (phiên bản 2)")
    print("4. optimized: Thông số tối ưu được tìm ra bởi hệ thống tự động (Error: 34.18)")
    print("5. low_distortion: Thông số cho distortion coefficients thấp")
    print("6. high_distortion: Thông số cho distortion coefficients cao")
