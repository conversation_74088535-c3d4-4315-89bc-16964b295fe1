import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def vectorxoay():
    v1 = np.array([-0.29368, 0.37142, 0.67755])
    v2 = np.array([0.075, 0, 0])

    u1 = v1 / np.linalg.norm(v1)
    u2 = v2 / np.linalg.norm(v2)

    n = np.cross(u1, u2)
    n = n / np.linalg.norm(n)

    cos_theta = np.dot(u1, u2)
    theta = np.arccos(cos_theta)
    N = np.array([[0, -n[2], n[1]],
                [n[2], 0, -n[0]],
                [-n[1], n[0], 0]])
    R = np.eye(3) + np.sin(theta) * N + (1 - np.cos(theta)) * np.dot(N, N)
    return R

def create_body_keypoints():
    # <PERSON><PERSON><PERSON> danh sách tọa độ 3D của các keypoints (đơn vị tương đối, ví dụ: mét)
    object_points = np.array([
        [-0.2, 0, 0],     # <PERSON><PERSON><PERSON> chân trái (0)
        [0.2, 0, 0],      # <PERSON><PERSON>t ch<PERSON> ph<PERSON>i (1)
        [-0.4, 0.1, 2],   # <PERSON><PERSON>u g<PERSON>i tr<PERSON>i (2)
        [0.4, 0.1, 2],    # <PERSON><PERSON>u g<PERSON>i ph<PERSON>i (3)
        [-0.7, 0, 4],    # <PERSON><PERSON>ng tr<PERSON>i (4)
        [0.7, 0, 4],     # <PERSON><PERSON>ng ph<PERSON>i (5)
        [-2, 0.1, 6.7], # Khuỷu tay trái (6)
        [2, 0.1, 6.7],  # Khuỷu tay phải (7)
        [-3, 0.1, 6.7], # Cổ tay trái (8)
        [3, 0.1, 6.7],  # Cổ tay phải (9)
        [-1, 0, 6.7],     # Vai trái (10)
        [1, 0, 6.7],      # Vai phải (11)
        [0, 0, 8]         # Đỉnh đầu (12)
    ], dtype=np.float32)
    object_points_1 = np.array([
        [-0.21272, -0.33251, 0.62928],      # Điểm 0
        [-0.20245, -0.34590, 0.63396],      # Điểm 1
        [-0.22383, -0.34167, 0.62080],      # Điểm 2  
        [-0.25296, -0.32470, 0.60454],      # Điểm 3
        [-0.17951, -0.25115, 0.66228],      # Điểm 4
        [-0.29599, -0.24167, 0.58895],      # Điểm 5
        [-0.074031, -0.26146, 0.72847],     # Điểm 6
        [-0.37490, -0.22710, 0.54056],      # Điểm 7
        [0.042017, -0.28760, 0.79919],      # Điểm 8
        [-0.41779, -0.21183, 0.51555],      # Điểm 9
        [-0.22067, 0.0063211, 0.67259],     # Điểm 10
        [-0.28759, 0.0009209, 0.62904],     # Điểm 11
        [-0.26244, 0.18726, 0.67152],       # Điểm 12
        [-0.30488, 0.18574, 0.64417],       # Điểm 13
        [-0.29368, 0.37142, 0.67755],       # Điểm 14
        [-0.31771, 0.37922, 0.66328]        # Điểm 15
    ], dtype=np.float32)
    R = vectorxoay()
    rotated_points = []
    for point in object_points_1:
        rotated_point = R @ point
        rotated_points.append(rotated_point)

    rotated_points = np.array(rotated_points)
    return rotated_points

def plot_3d_model():
    # Lấy tọa độ 3D
    object_points = create_body_keypoints()

    # Tạo figure và axes 3D
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # Vẽ các điểm (keypoints)
    ax.scatter(object_points[:, 0], object_points[:, 1], object_points[:, 2], c='red', s=100, label='Keypoints')

    # Kết nối các điểm để tạo cấu trúc cơ thể
    connections = [
        (9, 7), 
        (1, 3),  
        (6, 4),  
        (3, 5), 
        (4, 5),  
        (4, 10), 
        (5, 11), 
        (10, 11),
        (8, 6), 
        (5, 7), 
        (11, 13), 
        (10, 12),  
        (12, 14),
        (13, 15) 
    ]

    # Vẽ các đường nối
    for connection in connections:
        point1 = object_points[connection[0]]
        point2 = object_points[connection[1]]
        ax.plot(
            [point1[0], point2[0]],  # x
            [point1[1], point2[1]],  # y
            [point1[2], point2[2]],  # z
            c='blue'
        )

    # Đặt nhãn cho các trục
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')

    # Đặt tiêu đề
    ax.set_title('3D Model of Body Keypoints')

    # Đặt giới hạn cho các trục (tùy chỉnh theo tọa độ)
    ax.set_xlim([-0.5, 0.5])
    ax.set_ylim([-0.5, 0.5])
    ax.set_zlim([0, 1])

    # Thêm chú thích
    ax.legend()

    # Hiển thị đồ thị
    plt.show()

# Gọi hàm để vẽ
plot_3d_model()