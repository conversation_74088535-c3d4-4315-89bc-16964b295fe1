"""
<PERSON><PERSON>n bản đơn giản củ<PERSON> hệ thống tối ưu hóa thông số
"""

import numpy as np
import cv2
import os
import json
from datetime import datetime
import sys

# Thêm đường dẫn hiện tại vào sys.path để import đ<PERSON><PERSON><PERSON> các module local
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pre import create_body_keypoints, predict_yolo_pose_np_xy

def load_real_image_data(image_folder="human"):
    """Load dữ liệu thực từ ảnh"""
    print(f"Đang load dữ liệu ảnh từ thư mục: {image_folder}")
    
    image_points_list = []
    
    if not os.path.isdir(image_folder):
        print(f"Không tìm thấy thư mục: {image_folder}")
        return []
        
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')
    
    for filename in os.listdir(image_folder):
        image_path = os.path.join(image_folder, filename)
        if os.path.isfile(image_path) and filename.lower().endswith(supported_formats):
            print(f"  Đang xử lý: {filename}")
            try:
                keypoints = predict_yolo_pose_np_xy(image_path)
                if keypoints is not None and keypoints.shape == (12, 2):
                    image_points_list.append(keypoints.astype(np.float32))
                    print(f"    ✓ Thành công")
                else:
                    print(f"    ✗ Thất bại - keypoints không hợp lệ")
            except Exception as e:
                print(f"    ✗ Lỗi: {e}")
                
    print(f"Đã load thành công {len(image_points_list)} ảnh")
    return image_points_list

def test_calibration_with_params(params_dict, image_points_list):
    """Test hiệu chuẩn với một bộ thông số"""
    try:
        # Camera matrix cố định
        camera_matrix = np.array([
            [3257.479, 0, 4624/2],
            [0, 3264.984, 3468/2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Tạo object points với thông số
        object_points = create_body_keypoints(**params_dict)
        object_points_list = [object_points.astype(np.float32)] * len(image_points_list)
        
        # Khởi tạo distortion coefficients
        dist_coeffs = np.zeros((5, 1), dtype=np.float32)
        
        # Hiệu chuẩn camera
        ret, _, dist_coeffs, _, _ = cv2.calibrateCamera(
            object_points_list,
            image_points_list,
            (4624, 3468),
            camera_matrix.copy(),
            dist_coeffs,
            flags=cv2.CALIB_USE_INTRINSIC_GUESS | cv2.CALIB_FIX_FOCAL_LENGTH | cv2.CALIB_FIX_PRINCIPAL_POINT
        )
        
        return dist_coeffs.flatten(), ret
        
    except Exception as e:
        print(f"Lỗi trong test_calibration_with_params: {e}")
        return None, None

def manual_parameter_search(image_points_list, target_distortion):
    """Tìm kiếm thông số bằng cách thử nghiệm thủ công"""
    target_distortion = np.array(target_distortion)
    
    print("=== TÌM KIẾM THÔNG SỐ BẰNG CÁCH THỬ NGHIỆM ===")
    print(f"Target distortion: {target_distortion}")
    print(f"Số ảnh: {len(image_points_list)}")
    
    best_error = float('inf')
    best_params = None
    best_distortion = None
    
    # Định nghĩa các giá trị để thử nghiệm
    test_ranges = {
        'head_height_unit': [0.7, 0.8, 0.9, 1.0, 1.1],
        'body_height_factor': [7.8, 8.0, 8.2, 8.4],
        'shoulder_width_factor': [0.9, 1.0, 1.1, 1.2],
        'hip_width_factor_male': [0.7, 0.75, 0.8, 0.85],
        'elbow_outward_factor': [1.4, 1.6, 1.8, 2.0],
        'wrist_outward_factor': [1.8, 2.0, 2.2, 2.4],
        'limb_depth_factor': [0.06, 0.08, 0.1, 0.12],
        'knee_height_ratio': [0.45, 0.48, 0.5, 0.52]
    }
    
    # Thử nghiệm với nhiều tổ hợp để tìm ra kết quả gần với mục tiêu
    test_combinations = []

    # Tạo grid search với các giá trị quan trọng
    head_units = [0.95, 1.0, 1.05, 1.1]
    body_factors = [7.9, 8.0, 8.1, 8.2]
    shoulder_factors = [0.95, 1.0, 1.05, 1.1]
    elbow_factors = [1.3, 1.4, 1.5, 1.6]
    wrist_factors = [1.7, 1.8, 1.9, 2.0]

    # Tạo một số tổ hợp tốt
    for head in head_units:
        for body in body_factors:
            for shoulder in shoulder_factors:
                for elbow in elbow_factors:
                    for wrist in wrist_factors:
                        if len(test_combinations) < 20:  # Giới hạn số lượng test
                            test_combinations.append({
                                'head_height_unit': head,
                                'body_height_factor': body,
                                'shoulder_width_factor': shoulder,
                                'hip_width_factor_male': 0.75,
                                'arm_length_factor': 3.0,
                                'leg_length_factor': 4.0,
                                'elbow_outward_factor': elbow,
                                'wrist_outward_factor': wrist,
                                'limb_depth_factor': 0.1,
                                'knee_height_ratio': 0.5
                            })

    # Thêm một số tổ hợp đặc biệt
    special_combinations = [
        # Tổ hợp gần với target
        {
            'head_height_unit': 1.02,
            'body_height_factor': 8.05,
            'shoulder_width_factor': 1.02,
            'hip_width_factor_male': 0.76,
            'arm_length_factor': 3.02,
            'leg_length_factor': 4.02,
            'elbow_outward_factor': 1.42,
            'wrist_outward_factor': 1.82,
            'limb_depth_factor': 0.102,
            'knee_height_ratio': 0.502
        },
        # Tổ hợp với distortion nhỏ
        {
            'head_height_unit': 1.05,
            'body_height_factor': 7.95,
            'shoulder_width_factor': 0.98,
            'hip_width_factor_male': 0.73,
            'arm_length_factor': 3.05,
            'leg_length_factor': 3.98,
            'elbow_outward_factor': 1.38,
            'wrist_outward_factor': 1.78,
            'limb_depth_factor': 0.105,
            'knee_height_ratio': 0.505
        },
        # Tổ hợp với distortion âm
        {
            'head_height_unit': 1.08,
            'body_height_factor': 7.92,
            'shoulder_width_factor': 0.96,
            'hip_width_factor_male': 0.72,
            'arm_length_factor': 3.08,
            'leg_length_factor': 3.96,
            'elbow_outward_factor': 1.36,
            'wrist_outward_factor': 1.76,
            'limb_depth_factor': 0.108,
            'knee_height_ratio': 0.508
        }
    ]

    test_combinations.extend(special_combinations)
    
    for i, params in enumerate(test_combinations):
        print(f"\n--- Test tổ hợp {i+1} ---")
        print(f"Params: {params}")
        
        current_distortion, rms_error = test_calibration_with_params(params, image_points_list)
        
        if current_distortion is not None:
            error = np.linalg.norm(current_distortion - target_distortion)
            print(f"Current distortion: {current_distortion}")
            print(f"Target distortion:  {target_distortion}")
            print(f"Error: {error:.6f}")
            print(f"RMS Error: {rms_error:.6f}")
            
            if error < best_error:
                best_error = error
                best_params = params.copy()
                best_distortion = current_distortion.copy()
                print("*** ĐÂY LÀ KẾT QUẢ TỐT NHẤT HIỆN TẠI ***")
        else:
            print("Lỗi trong hiệu chuẩn")
    
    return best_params, best_distortion, best_error

def save_results(best_params, best_distortion, best_error, target_distortion):
    """Lưu kết quả tốt nhất"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"manual_optimal_params_{timestamp}.json"
    
    data = {
        'optimal_parameters': best_params,
        'achieved_distortion': best_distortion.tolist() if best_distortion is not None else None,
        'target_distortion': target_distortion.tolist(),
        'final_error': float(best_error),
        'timestamp': datetime.now().isoformat(),
        'method': 'manual_search'
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
        
    print(f"\nĐã lưu kết quả vào: {filename}")
    return filename

def create_realistic_synthetic_data():
    """Tạo dữ liệu giả thực tế hơn dựa trên kích thước ảnh thực"""
    print("Tạo dữ liệu giả thực tế...")

    image_points_list = []

    # Tạo 6 bộ dữ liệu với các pose khác nhau
    base_poses = [
        # Pose 1: Đứng thẳng, tay duỗi ngang
        np.array([
            [2200, 3400], [2400, 3400],  # heels
            [2150, 2800], [2450, 2800],  # knees
            [2100, 2200], [2500, 2200],  # hips
            [1800, 1500], [2800, 1500],  # elbows
            [1500, 1500], [3100, 1500],  # wrists
            [2000, 1500], [2600, 1500]   # shoulders
        ], dtype=np.float32),

        # Pose 2: Hơi nghiêng
        np.array([
            [2180, 3380], [2420, 3420],  # heels
            [2130, 2780], [2470, 2820],  # knees
            [2080, 2180], [2520, 2220],  # hips
            [1780, 1480], [2820, 1520],  # elbows
            [1480, 1480], [3120, 1520],  # wrists
            [1980, 1480], [2620, 1520]   # shoulders
        ], dtype=np.float32),

        # Pose 3: Tay hơi cong
        np.array([
            [2220, 3420], [2380, 3380],  # heels
            [2170, 2820], [2430, 2780],  # knees
            [2120, 2220], [2480, 2180],  # hips
            [1820, 1520], [2780, 1480],  # elbows
            [1520, 1520], [3080, 1480],  # wrists
            [2020, 1520], [2580, 1480]   # shoulders
        ], dtype=np.float32),

        # Pose 4: Chân hơi rộng
        np.array([
            [2150, 3400], [2450, 3400],  # heels
            [2100, 2800], [2500, 2800],  # knees
            [2050, 2200], [2550, 2200],  # hips
            [1750, 1500], [2850, 1500],  # elbows
            [1450, 1500], [3150, 1500],  # wrists
            [1950, 1500], [2650, 1500]   # shoulders
        ], dtype=np.float32),

        # Pose 5: Hơi xa camera
        np.array([
            [2250, 3350], [2350, 3350],  # heels
            [2200, 2750], [2400, 2750],  # knees
            [2150, 2150], [2450, 2150],  # hips
            [1850, 1450], [2750, 1450],  # elbows
            [1550, 1450], [3050, 1450],  # wrists
            [2050, 1450], [2550, 1450]   # shoulders
        ], dtype=np.float32),

        # Pose 6: Hơi gần camera
        np.array([
            [2100, 3450], [2500, 3450],  # heels
            [2050, 2850], [2550, 2850],  # knees
            [2000, 2250], [2600, 2250],  # hips
            [1700, 1550], [2900, 1550],  # elbows
            [1400, 1550], [3200, 1550],  # wrists
            [1900, 1550], [2700, 1550]   # shoulders
        ], dtype=np.float32)
    ]

    # Thêm nhiễu ngẫu nhiên cho mỗi pose
    for i, base_pose in enumerate(base_poses):
        noise = np.random.normal(0, 15, base_pose.shape)  # Nhiễu 15 pixel
        noisy_pose = base_pose + noise

        # Đảm bảo tọa độ nằm trong phạm vi ảnh
        noisy_pose[:, 0] = np.clip(noisy_pose[:, 0], 100, 4500)
        noisy_pose[:, 1] = np.clip(noisy_pose[:, 1], 100, 3300)

        image_points_list.append(noisy_pose.astype(np.float32))
        print(f"  Tạo pose {i+1}: ✓")

    print(f"Đã tạo {len(image_points_list)} bộ dữ liệu giả thực tế")
    return image_points_list

def main():
    # Distortion coefficients mục tiêu
    target_distortion = [1.07834685e-01, 1.11976029e-01, -1.44669794e-03, -1.83572338e-04, -1.34815082e+00]

    print("=== HỆ THỐNG TỐI ƯU HÓA THÔNG SỐ ĐỂN GIẢN ===")
    print(f"Distortion coefficients mục tiêu: {target_distortion}")

    # Thử load dữ liệu ảnh thực trước
    image_points_list = load_real_image_data()

    if len(image_points_list) < 3:
        print("Không đủ ảnh thực, sử dụng dữ liệu giả thực tế...")
        image_points_list = create_realistic_synthetic_data()

    # Tìm kiếm thông số tốt nhất
    best_params, best_distortion, best_error = manual_parameter_search(image_points_list, target_distortion)

    if best_params is not None:
        print(f"\n=== KẾT QUẢ TỐT NHẤT ===")
        print(f"Error: {best_error:.6f}")
        print(f"Achieved distortion: {best_distortion}")
        print(f"Target distortion:   {target_distortion}")
        print(f"Best parameters:")
        for key, value in best_params.items():
            print(f"  {key}: {value}")

        # Lưu kết quả
        save_results(best_params, best_distortion, best_error, np.array(target_distortion))
    else:
        print("Không tìm được thông số phù hợp")

if __name__ == "__main__":
    main()
