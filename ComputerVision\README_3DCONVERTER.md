# Hệ Thống Tái Dựng 3D và Thiết Lập Hệ Tọa Độ Thế Giới

## Tổng Quan

File `3dconverter.py` thực hiện tái dựng 3D từ hai ảnh stereo và thiết lập hệ tọa độ thế giới với **mặt đất làm tham chiếu** (Z=0). Đ<PERSON>y là cải tiến quan trọng so với phiên bản trước chỉ tái dựng trong hệ tọa độ camera.

## Tính Năng Chính

### 🎯 **Thiết Lập Hệ Tọa Độ Thế Giới**
- **Mặt đất làm tham chiếu**: <PERSON><PERSON><PERSON> điểm chân được đặt ở Z=0
- **Gốc tọa độ**: Trung tâm giữa hai chân
- **Trục Z**: Hướng lên trên (ngược với trọng lực)
- **Tự động điều chỉnh**: Phát hiện và sửa hướng trục Z nếu bị đảo ngược

### 📏 **T<PERSON>h Toán Thông Số Cơ Thể Thực Tế**
- Chiều cao tổng thể (từ mặt đất đến điểm cao nhất)
- Chiều rộng vai
- Chiều dài cánh tay trái/phải
- Chiều dài chân trái/phải
- Chiều cao hông và vai
- Khoảng cách giữa hai chân

### 📊 **Hiển Thị và Lưu Trữ**
- Hiển thị 3D interactive với matplotlib
- Lưu kết quả chi tiết vào file text
- Chiếu ngược về mặt phẳng ảnh để kiểm tra
- Hiển thị ảnh với keypoints được đánh dấu

## Cách Sử Dụng

### Chạy Chương Trình
```bash
python ComputerVision/3dconverter.py
```

### Input Required
- **Hai ảnh stereo**: 
  - `human_frame_and/frame_001608.jpg`
  - `human_frame_ip/frame_001140.jpg`
- **Model YOLO**: `yolo11n-pose.pt` (tự động tải xuống)

### Output
1. **Console**: Hiển thị tọa độ 3D và thông số cơ thể
2. **File**: `3d_reconstruction_results.txt` - Kết quả chi tiết
3. **3D Plot**: Hiển thị interactive với mặt phẳng đất
4. **2D Image**: Ảnh với keypoints được đánh dấu

## Cấu Trúc Code

### 1. **Phát Hiện Keypoints**
```python
# Sử dụng YOLO11 để phát hiện pose
model = YOLO("yolo11n-pose.pt")
results1 = model(image1_path)
results2 = model(image2_path)
```

### 2. **Tái Dựng 3D**
```python
# Triangulation từ hai view
points_4d = cv2.triangulatePoints(proj_matrix1, proj_matrix2, points1.T, points2.T)
points_3d = points_4d[:3] / points_4d[3]
```

### 3. **Thiết Lập Hệ Tọa Độ Thế Giới**
```python
# Tìm điểm chân và thiết lập mặt đất
points_3d_world, transformation_matrix = establish_world_coordinate_system(points_3d, keypoint_names)
```

### 4. **Tính Toán Thông Số Cơ Thể**
```python
# Đo đạc các thông số thực tế
body_measurements = calculate_body_measurements(points_3d_world, keypoint_names)
```

## Các Hàm Chính

### `establish_world_coordinate_system()`
- **Mục đích**: Chuyển đổi từ hệ tọa độ camera sang hệ tọa độ thế giới
- **Tham chiếu**: Mặt đất (Z=0) và trung tâm chân
- **Xử lý**: Tự động phát hiện và sửa hướng trục Z

### `calculate_body_measurements()`
- **Mục đích**: Tính toán các thông số cơ thể thực tế
- **Đơn vị**: Mét (m)
- **Thông số**: Chiều cao, chiều rộng, chiều dài các bộ phận

### `visualize_3d_points()`
- **Mục đích**: Hiển thị 3D interactive
- **Tính năng**: Mặt phẳng đất, nhãn keypoints, zoom/pan

### `save_results_to_file()`
- **Mục đích**: Lưu kết quả chi tiết
- **Format**: Text file có cấu trúc
- **Nội dung**: Tọa độ, ma trận chuyển đổi, thông số cơ thể

## Kết Quả Mẫu

### Tọa Độ 3D (Hệ Tọa Độ Thế Giới)
```
 0. nose           : ( 0.091, -0.715, -0.028) m
 1. left_eye       : ( 0.102, -0.728, -0.023) m
 2. right_eye      : ( 0.080, -0.724, -0.036) m
...
14. right_knee     : ( 0.012, -0.004,  0.000) m
15. left_ankle     : (-0.012,  0.004,  0.000) m
```

### Thông Số Cơ Thể
```
Chiều cao tổng thể: 0.127 m (điểm cao nhất: right_elbow)
Chiều rộng vai: 0.262 m
Chiều dài cánh tay trái: 0.149 m
Chiều dài cánh tay phải: 0.311 m
Chiều dài chân trái: 0.383 m
```

## Ưu Điểm So Với Phiên Bản Trước

### ✅ **Có Tham Chiếu Thực Tế**
- **Trước**: Chỉ có tọa độ trong hệ camera (không có ý nghĩa vật lý)
- **Sau**: Hệ tọa độ thế giới với mặt đất làm tham chiếu

### ✅ **Thông Số Có Ý Nghĩa**
- **Trước**: Tọa độ tương đối không rõ ràng
- **Sau**: Thông số cơ thể thực tế tính bằng mét

### ✅ **Tự Động Xử Lý**
- **Trước**: Cần xử lý thủ công
- **Sau**: Tự động phát hiện chân, sửa hướng trục, thiết lập hệ tọa độ

### ✅ **Lưu Trữ Kết Quả**
- **Trước**: Chỉ hiển thị console
- **Sau**: Lưu file chi tiết, hiển thị 3D, xuất ảnh

## Hạn Chế và Cải Tiến

### 🔄 **Hạn Chế Hiện Tại**
1. **Phụ thuộc vào chất lượng keypoint detection**
2. **Cần ít nhất 2 điểm chân để thiết lập mặt đất**
3. **Độ chính xác phụ thuộc vào hiệu chuẩn camera**

### 🚀 **Cải Tiến Có Thể**
1. **Sử dụng nhiều frame để tăng độ chính xác**
2. **Thêm filter để làm mượt dữ liệu**
3. **Tích hợp với hệ thống tracking**
4. **Thêm validation cho thông số cơ thể**

## Dependencies

```bash
pip install opencv-python numpy matplotlib ultralytics
```

## File Structure

```
ComputerVision/
├── 3dconverter.py              # File chính
├── README_3DCONVERTER.md       # Hướng dẫn này
├── human_frame_and/            # Ảnh camera 1
├── human_frame_ip/             # Ảnh camera 2
└── 3d_reconstruction_results.txt # Kết quả output
```

## Kết Luận

Hệ thống đã được nâng cấp thành công để có **tham chiếu tọa độ 3D thực tế** với mặt đất làm mốc. Điều này cho phép:

- **Đo đạc thông số cơ thể chính xác**
- **Thiết lập hệ tọa độ có ý nghĩa vật lý**
- **Ứng dụng trong các bài toán thực tế**

Đây là bước tiến quan trọng từ việc chỉ tái dựng 3D đơn thuần sang một hệ thống đo đạc thực tế có thể ứng dụng.
