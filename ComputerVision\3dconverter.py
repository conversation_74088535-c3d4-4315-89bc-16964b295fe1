import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>
from scipy.spatial.transform import Rotation as R
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def get_yolo_keypoint_names():
    """
    <PERSON><PERSON><PERSON> về danh sách tên keypoints theo thứ tự chuẩn YOLO (17 điểm)
    """
    return [
        'nose',           # 0
        'left_eye',       # 1
        'right_eye',      # 2
        'left_ear',       # 3
        'right_ear',      # 4
        'left_shoulder',  # 5
        'right_shoulder', # 6
        'left_elbow',     # 7
        'right_elbow',    # 8
        'left_wrist',     # 9
        'right_wrist',    # 10
        'left_hip',       # 11
        'right_hip',      # 12
        'left_knee',      # 13
        'right_knee',     # 14
        'left_ankle',     # 15
        'right_ankle'     # 16
    ]

def get_ordered_matched_keypoints(img1_path, img2_path, model_path, confidence_thresh=0.5):
    model = YOLO(model_path)
    results1 = model(img1_path)[0]
    results2 = model(img2_path)[0]

    kpts1 = []
    kpts2 = []
    idx_map = []

    if results1.keypoints is not None and results2.keypoints is not None:
        keypoints1 = results1.keypoints.data.numpy()
        keypoints2 = results2.keypoints.data.numpy()

        if len(keypoints1) > 0 and len(keypoints2) > 0:
            person1 = keypoints1[0]  # người đầu tiên trong ảnh 1
            person2 = keypoints2[0]  # người đầu tiên trong ảnh 2

            for i in range(17):  # theo thứ tự chuẩn YOLO
                if person1[i][2] > confidence_thresh and person2[i][2] > confidence_thresh:
                    kpts1.append([person1[i][0], person1[i][1]])
                    kpts2.append([person2[i][0], person2[i][1]])
                    idx_map.append(i)

    points1 = np.float32(kpts1).reshape(-1, 1, 2)
    points2 = np.float32(kpts2).reshape(-1, 1, 2)

    return points1, points2, idx_map

def reconstruct_3d(img1_path, img2_path, model_path, K1, K2):
    points1, points2, idx_map = get_ordered_matched_keypoints(img1_path, img2_path, model_path)

    if len(points1) < 5:
        raise ValueError("Not enough matched keypoints for triangulation.")

    F, _ = cv2.findFundamentalMat(points1, points2, cv2.FM_RANSAC)
    E = K2.T @ F @ K1

    _, R_rel, t_rel, _ = cv2.recoverPose(E, points1, points2, K1)

    proj_matrix1 = K1 @ np.hstack((np.eye(3), np.zeros((3, 1))))
    proj_matrix2 = K2 @ np.hstack((R_rel, t_rel))
    points_4d = cv2.triangulatePoints(proj_matrix1, proj_matrix2, points1.T, points2.T)
    w = points_4d[3]
    mask = np.abs(w) > 1e-6
    if np.count_nonzero(mask) < 5:
        raise ValueError("Too few valid 3D points recovered. Check keypoint matching.")
    points_3d = (points_4d[:3][:, mask] / w[mask]).T

    return points_3d, idx_map

def align_pose_to_standard(points_3d, idx_map):
    """
    Xoay bộ xương đứng thẳng (dọc trục Y) và dịch chuyển sao cho hông là gốc tọa độ

    Args:
        points_3d: Mảng tọa độ 3D (N, 3)
        idx_map: Danh sách chỉ số keypoints theo thứ tự YOLO

    Returns:
        aligned_points: Tọa độ 3D đã được căn chỉnh
        transformation_info: Thông tin về phép biến đổi
    """
    print("=== CĂNG CHỈNH BỘ XƯƠNG THEO CHUẨN ===")

    # Tạo dictionary để truy cập điểm theo chỉ số YOLO
    points_dict = {idx: points_3d[i] for i, idx in enumerate(idx_map)}
    keypoint_names = get_yolo_keypoint_names()

    print(f"Số điểm 3D có sẵn: {len(points_3d)}")
    print("Các keypoints được sử dụng:")
    for i, idx in enumerate(idx_map):
        print(f"  {idx:2d}. {keypoint_names[idx]:15s}: {points_3d[i]}")

    # Kiểm tra các điểm cần thiết cho alignment
    required_points = [11, 12, 15, 16]  # left_hip, right_hip, left_ankle, right_ankle
    missing_points = [idx for idx in required_points if idx not in points_dict]

    if missing_points:
        print(f"Cảnh báo: Thiếu các điểm quan trọng: {[keypoint_names[idx] for idx in missing_points]}")
        # Sử dụng phương pháp thay thế
        return align_pose_alternative(points_3d, idx_map)

    # Lấy các điểm quan trọng
    hip_left = points_dict[11]    # left_hip
    hip_right = points_dict[12]   # right_hip
    ankle_left = points_dict[15]  # left_ankle
    ankle_right = points_dict[16] # right_ankle

    # Tính trung tâm hông (sẽ là gốc tọa độ mới)
    hip_center = (hip_left + hip_right) / 2
    print(f"Trung tâm hông (gốc tọa độ mới): {hip_center}")

    # Tính trung tâm mắt cá chân
    ankle_center = (ankle_left + ankle_right) / 2
    print(f"Trung tâm mắt cá chân: {ankle_center}")

    # Vector từ chân lên hông (hướng lên)
    up_vector = hip_center - ankle_center
    up_vector_norm = np.linalg.norm(up_vector)

    if up_vector_norm < 1e-6:
        print("Cảnh báo: Vector hướng lên quá ngắn, sử dụng trục Y mặc định")
        up_vector = np.array([0, 1, 0])
    else:
        up_vector = up_vector / up_vector_norm

    print(f"Vector hướng lên hiện tại: {up_vector}")

    # Vector mục tiêu (trục Y dương)
    target_up = np.array([0, 1, 0])

    # Tính góc xoay để căn chỉnh
    dot_product = np.clip(np.dot(up_vector, target_up), -1, 1)
    angle = np.arccos(dot_product)

    print(f"Góc cần xoay: {np.degrees(angle):.2f} độ")

    # Tính trục xoay
    axis = np.cross(up_vector, target_up)
    axis_norm = np.linalg.norm(axis)

    if axis_norm > 1e-6:
        # Có cần xoay
        axis = axis / axis_norm
        rotation = R.from_rotvec(axis * angle)
        aligned_points = rotation.apply(points_3d)
        print(f"Trục xoay: {axis}")
    else:
        # Đã thẳng hàng hoặc ngược hướng
        if dot_product < 0:
            # Ngược hướng, xoay 180 độ quanh trục X
            rotation = R.from_rotvec([np.pi, 0, 0])
            aligned_points = rotation.apply(points_3d)
            print("Xoay 180 độ quanh trục X")
        else:
            # Đã thẳng hàng
            aligned_points = points_3d.copy()
            rotation = R.from_rotvec([0, 0, 0])
            print("Không cần xoay")

    # Dịch chuyển để hông là gốc tọa độ
    # Tính lại trung tâm hông sau khi xoay
    hip_left_aligned = aligned_points[idx_map.index(11)]
    hip_right_aligned = aligned_points[idx_map.index(12)]
    hip_center_aligned = (hip_left_aligned + hip_right_aligned) / 2

    aligned_points = aligned_points - hip_center_aligned

    print(f"Dịch chuyển về gốc tọa độ: -{hip_center_aligned}")

    # Thông tin về phép biến đổi
    transformation_info = {
        'original_hip_center': hip_center,
        'rotation': rotation,
        'translation': -hip_center_aligned,
        'up_vector_original': up_vector,
        'angle_degrees': np.degrees(angle)
    }

    return aligned_points, transformation_info

def align_pose_alternative(points_3d, idx_map):
    """
    Phương pháp căn chỉnh thay thế khi thiếu một số điểm quan trọng
    """
    print("Sử dụng phương pháp căn chỉnh thay thế...")

    points_dict = {idx: points_3d[i] for i, idx in enumerate(idx_map)}

    # Tìm điểm cao nhất và thấp nhất
    z_coords = points_3d[:, 2]
    highest_idx = np.argmax(z_coords)
    lowest_idx = np.argmin(z_coords)

    highest_point = points_3d[highest_idx]
    lowest_point = points_3d[lowest_idx]

    # Vector từ thấp đến cao
    up_vector = highest_point - lowest_point
    up_vector = up_vector / np.linalg.norm(up_vector)

    # Xoay để thẳng đứng
    target_up = np.array([0, 1, 0])
    axis = np.cross(up_vector, target_up)
    angle = np.arccos(np.clip(np.dot(up_vector, target_up), -1, 1))

    if np.linalg.norm(axis) > 1e-6:
        rotation = R.from_rotvec(axis / np.linalg.norm(axis) * angle)
        aligned_points = rotation.apply(points_3d)
    else:
        aligned_points = points_3d.copy()
        rotation = R.from_rotvec([0, 0, 0])

    # Dịch chuyển để điểm trung tâm là gốc
    center = np.mean(aligned_points, axis=0)
    aligned_points = aligned_points - center

    transformation_info = {
        'method': 'alternative',
        'rotation': rotation,
        'translation': -center,
        'highest_point': highest_point,
        'lowest_point': lowest_point
    }

    return aligned_points, transformation_info

# =================== Dùng thử ===================
K1 = np.array([[955.64, 0, 784.75], [0, 902.67, 1040.39], [0, 0, 1]])
K2 = np.array([[1328.61, 0, 507.68], [0, 1331.32, 951.98], [0, 0, 1]])

img1_path = "D:\\AI\\CameraCalib\\human_frame_and\\frame_001608.jpg"
img2_path = "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001140.jpg"
model_path = "D:\\AI\\CameraCalib\\ComputerVision\\yolov8n-pose.pt"

points_3d, idx_map = reconstruct_3d(img1_path, img2_path, model_path, K1, K2)
aligned_points = align_pose(points_3d, idx_map)

print("3D pose aligned to upright:\n", aligned_points)
