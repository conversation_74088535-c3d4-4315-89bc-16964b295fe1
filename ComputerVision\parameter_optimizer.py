"""
<PERSON><PERSON> thống tối ưu hóa tự động để tìm bộ thông số tốt nhất cho distortion coefficients mục tiêu
"""

import numpy as np
import cv2
from scipy.optimize import minimize, differential_evolution
import os
import json
from datetime import datetime
import sys

# Thêm đường dẫn hiện tại vào sys.path để import đư<PERSON><PERSON> các module local
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pre import create_body_keypoints, predict_yolo_pose_np_xy

class ParameterOptimizer:
    def __init__(self, target_distortion, image_folder="human", camera_matrix=None, image_size=(4624, 3468)):
        """
        Khởi tạo optimizer
        
        Args:
            target_distortion: Distortion coefficients mục tiêu [k1, k2, p1, p2, k3]
            image_folder: Th<PERSON> mục chứa ảnh để hiệu chuẩn
            camera_matrix: Ma trận camera (sẽ đư<PERSON><PERSON> cố định)
            image_size: <PERSON><PERSON><PERSON> thước <PERSON>
        """
        self.target_distortion = np.array(target_distortion).flatten()
        self.image_folder = image_folder
        self.image_size = image_size
        
        if camera_matrix is None:
            self.camera_matrix = np.array([
                [3257.479, 0, 4624/2],
                [0, 3264.984, 3468/2],
                [0, 0, 1]
            ], dtype=np.float32)
        else:
            self.camera_matrix = camera_matrix
            
        # Load image data một lần
        self.image_points_list = []
        self.load_image_data()
        
        # Định nghĩa bounds cho các thông số
        self.parameter_bounds = {
            'head_height_unit': (0.5, 1.5),
            'body_height_factor': (7.0, 9.0),
            'shoulder_width_factor': (0.8, 1.5),
            'hip_width_factor_male': (0.6, 1.0),
            'arm_length_factor': (2.0, 4.0),
            'leg_length_factor': (3.5, 4.5),
            'elbow_outward_factor': (0.8, 2.5),
            'wrist_outward_factor': (1.2, 3.0),
            'limb_depth_factor': (0.01, 0.2),
            'knee_height_ratio': (0.3, 0.7)
        }
        
    def load_image_data(self):
        """Load và xử lý tất cả ảnh một lần"""
        print(f"Đang load dữ liệu ảnh từ thư mục: {self.image_folder}")

        if not os.path.isdir(self.image_folder):
            raise ValueError(f"Không tìm thấy thư mục: {self.image_folder}")

        supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')

        for filename in os.listdir(self.image_folder):
            image_path = os.path.join(self.image_folder, filename)
            if os.path.isfile(image_path) and filename.lower().endswith(supported_formats):
                print(f"  Đang xử lý: {filename}")
                try:
                    keypoints = predict_yolo_pose_np_xy(image_path)
                    if keypoints is not None and keypoints.shape == (12, 2):
                        self.image_points_list.append(keypoints)
                        print(f"    ✓ Thành công")
                    else:
                        print(f"    ✗ Thất bại - keypoints không hợp lệ")
                except Exception as e:
                    print(f"    ✗ Lỗi: {e}")

        print(f"Đã load thành công {len(self.image_points_list)} ảnh")

        if len(self.image_points_list) < 3:
            # Tạo dữ liệu giả để test
            print("Không đủ ảnh thực, tạo dữ liệu giả để test...")
            self.create_synthetic_data()

    def create_synthetic_data(self):
        """Tạo dữ liệu giả để test hệ thống tối ưu hóa"""
        print("Tạo 5 bộ dữ liệu image points giả...")

        # Tạo 5 bộ image points giả với một chút nhiễu
        base_points = np.array([
            [2200, 3400], [2400, 3400],  # heels
            [2150, 2800], [2450, 2800],  # knees
            [2100, 2200], [2500, 2200],  # hips
            [1800, 1500], [2800, 1500],  # elbows
            [1500, 1500], [3100, 1500],  # wrists
            [2000, 1500], [2600, 1500]   # shoulders
        ], dtype=np.float32)

        for i in range(5):
            # Thêm nhiễu ngẫu nhiên
            noise = np.random.normal(0, 20, base_points.shape)
            noisy_points = base_points + noise
            self.image_points_list.append(noisy_points)

        print(f"Đã tạo {len(self.image_points_list)} bộ dữ liệu giả")
    
    def parameters_to_array(self, params_dict):
        """Chuyển dictionary thông số thành array để tối ưu hóa"""
        param_names = list(self.parameter_bounds.keys())
        return np.array([params_dict[name] for name in param_names])
    
    def array_to_parameters(self, params_array):
        """Chuyển array thành dictionary thông số"""
        param_names = list(self.parameter_bounds.keys())
        return {name: float(params_array[i]) for i, name in enumerate(param_names)}
    
    def objective_function(self, params_array):
        """
        Hàm mục tiêu: tính khoảng cách giữa distortion coefficients hiện tại và mục tiêu
        """
        try:
            # Chuyển array thành dictionary
            params_dict = self.array_to_parameters(params_array)
            
            # Tạo object points với thông số hiện tại
            object_points = create_body_keypoints(**params_dict)
            
            # Tạo danh sách object points cho tất cả ảnh
            object_points_list = [object_points] * len(self.image_points_list)
            
            # Khởi tạo distortion coefficients
            dist_coeffs = np.zeros((5, 1), dtype=np.float32)
            
            # Đảm bảo dữ liệu đúng định dạng cho OpenCV
            object_points_list_cv = [obj_pts.astype(np.float32) for obj_pts in object_points_list]
            image_points_list_cv = [img_pts.astype(np.float32) for img_pts in self.image_points_list]

            # Hiệu chuẩn camera
            ret, _, dist_coeffs, _, _ = cv2.calibrateCamera(
                object_points_list_cv,
                image_points_list_cv,
                self.image_size,
                self.camera_matrix.copy(),
                dist_coeffs,
                flags=cv2.CALIB_USE_INTRINSIC_GUESS | cv2.CALIB_FIX_FOCAL_LENGTH | cv2.CALIB_FIX_PRINCIPAL_POINT
            )
            
            # Tính khoảng cách với distortion mục tiêu
            current_distortion = dist_coeffs.flatten()
            error = np.linalg.norm(current_distortion - self.target_distortion)
            
            print(f"Params: {[f'{v:.3f}' for v in params_array]}")
            print(f"Current distortion: {current_distortion}")
            print(f"Target distortion:  {self.target_distortion}")
            print(f"Error: {error:.6f}")
            print("-" * 50)
            
            return error
            
        except Exception as e:
            print(f"Lỗi trong objective function: {e}")
            return 1e6  # Trả về giá trị lớn nếu có lỗi
    
    def optimize_differential_evolution(self, maxiter=50, popsize=15):
        """
        Tối ưu hóa bằng Differential Evolution (tốt cho global optimization)
        """
        print("=== BẮT ĐẦU TỐI ƯU HÓA BẰNG DIFFERENTIAL EVOLUTION ===")
        print(f"Target distortion: {self.target_distortion}")
        print(f"Số ảnh: {len(self.image_points_list)}")
        print(f"Max iterations: {maxiter}, Population size: {popsize}")
        print()
        
        # Tạo bounds cho scipy
        bounds = [self.parameter_bounds[name] for name in self.parameter_bounds.keys()]
        
        # Chạy tối ưu hóa
        result = differential_evolution(
            self.objective_function,
            bounds,
            maxiter=maxiter,
            popsize=popsize,
            seed=42,
            disp=True
        )
        
        # Chuyển kết quả thành dictionary
        optimal_params = self.array_to_parameters(result.x)
        
        print("\n=== KẾT QUẢ TỐI ƯU HÓA ===")
        print(f"Thành công: {result.success}")
        print(f"Error cuối cùng: {result.fun:.6f}")
        print(f"Số lần đánh giá: {result.nfev}")
        print("\nThông số tối ưu:")
        for name, value in optimal_params.items():
            print(f"  {name}: {value:.4f}")
            
        return optimal_params, result.fun
    
    def save_optimal_parameters(self, optimal_params, error, filename=None):
        """Lưu thông số tối ưu vào file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"optimal_params_{timestamp}.json"
            
        data = {
            'optimal_parameters': optimal_params,
            'target_distortion': self.target_distortion.tolist(),
            'final_error': float(error),
            'timestamp': datetime.now().isoformat(),
            'num_images': len(self.image_points_list)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        print(f"Đã lưu thông số tối ưu vào: {filename}")
        return filename

def main():
    # Distortion coefficients mục tiêu
    target_distortion = [1.07834685e-01, 1.11976029e-01, -1.44669794e-03, -1.83572338e-04, -1.34815082e+00]
    
    print("=== HỆ THỐNG TỐI ƯU HÓA THÔNG SỐ TỰ ĐỘNG ===")
    print(f"Distortion coefficients mục tiêu: {target_distortion}")
    
    try:
        # Khởi tạo optimizer
        optimizer = ParameterOptimizer(target_distortion)
        
        # Chạy tối ưu hóa
        optimal_params, final_error = optimizer.optimize_differential_evolution(maxiter=30, popsize=10)
        
        # Lưu kết quả
        filename = optimizer.save_optimal_parameters(optimal_params, final_error)
        
        print(f"\n=== HOÀN THÀNH ===")
        print(f"Thông số tối ưu đã được lưu vào: {filename}")
        print(f"Error cuối cùng: {final_error:.6f}")
        
    except Exception as e:
        print(f"Lỗi: {e}")

if __name__ == "__main__":
    main()
