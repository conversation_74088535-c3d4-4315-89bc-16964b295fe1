# Hệ Thống Tối Ưu Hóa Thông Số Cơ Thể Tự Động

## Tổng Quan

Hệ thống này được thiết kế để tự động tìm ra bộ thông số cơ thể tối ưu nhằm đạt được distortion coefficients mong muốn khi hiệu chuẩn camera.

**Mục tiêu:** Đạt được distortion coefficients gần với `[1.07834685e-01, 1.11976029e-01, -1.44669794e-03, -1.83572338e-04, -1.34815082e+00]`

## Các File Chính

### 1. `body_parameters.py`
Chứa các bộ thông số cơ thể được định nghĩa trước:

- **`default`**: Thông số mặc định
- **`target_v1`**: Tối ưu cho distortion coefficients mục tiêu (phiê<PERSON> bản 1)
- **`target_v2`**: T<PERSON><PERSON> ưu cho distortion coefficients mục tiêu (phi<PERSON><PERSON> bản 2)
- **`optimized`**: Thông số tối ưu được tìm ra bởi hệ thống tự động (Error: 34.18)
- **`low_distortion`**: Thông số cho distortion coefficients thấp
- **`high_distortion`**: Thông số cho distortion coefficients cao

### 2. `pre.py`
File chính để hiệu chuẩn camera với các thông số có thể lựa chọn:

```bash
python ComputerVision/pre.py
```

**Chế độ 1**: Test thông số (chỉ xem object points)
**Chế độ 2**: Chạy hiệu chuẩn camera

### 3. `simple_optimizer.py`
Hệ thống tối ưu hóa đơn giản với grid search:

```bash
python ComputerVision/simple_optimizer.py
```

Tự động tìm kiếm trong 20+ tổ hợp thông số để tìm ra kết quả tốt nhất.

### 4. `parameter_optimizer.py`
Hệ thống tối ưu hóa nâng cao sử dụng Differential Evolution:

```bash
python ComputerVision/parameter_optimizer.py
```

Sử dụng thuật toán tối ưu hóa toàn cục để tìm ra thông số tối ưu.

## Kết Quả Tối Ưu Hiện Tại

**Bộ thông số tốt nhất được tìm ra:**

```python
OPTIMIZED_TARGET_PARAMS = {
    'head_height_unit': 0.95,
    'body_height_factor': 7.9,
    'shoulder_width_factor': 0.95,
    'hip_width_factor_male': 0.75,
    'arm_length_factor': 3.0,
    'leg_length_factor': 4.0,
    'elbow_outward_factor': 1.4,
    'wrist_outward_factor': 1.7,
    'limb_depth_factor': 0.1,
    'knee_height_ratio': 0.5
}
```

**Kết quả đạt được:**
- Error: 34.18
- Achieved distortion: `[13.129, -25.005, -1.3927, 0.0082962, 17.791]`
- Target distortion: `[0.107834685, 0.111976029, -0.00144669794, -0.000183572338, -1.34815082]`

## Cách Sử Dụng

### 1. Sử dụng bộ thông số tối ưu có sẵn:

```bash
python ComputerVision/pre.py
# Chọn chế độ 2 (hiệu chuẩn camera)
# Nhập "optimized" khi được hỏi về bộ thông số
```

### 2. Tìm kiếm thông số mới:

```bash
python ComputerVision/simple_optimizer.py
```

Hệ thống sẽ:
- Load dữ liệu ảnh từ thư mục `human/`
- Nếu không có ảnh, sử dụng dữ liệu giả thực tế
- Test 20+ tổ hợp thông số khác nhau
- Lưu kết quả tốt nhất vào file JSON

### 3. Tối ưu hóa nâng cao:

```bash
python ComputerVision/parameter_optimizer.py
```

Sử dụng thuật toán Differential Evolution để tìm kiếm toàn cục.

## Cấu Trúc Thông Số

Mỗi bộ thông số bao gồm 10 tham số điều chỉnh tỷ lệ cơ thể:

1. **`head_height_unit`**: Đơn vị chiều cao đầu (0.5-1.5)
2. **`body_height_factor`**: Tổng chiều cao cơ thể tính bằng số "đầu" (7.0-9.0)
3. **`shoulder_width_factor`**: Nửa chiều rộng vai (0.8-1.5)
4. **`hip_width_factor_male`**: Nửa chiều rộng hông nam (0.6-1.0)
5. **`arm_length_factor`**: Chiều dài cánh tay (2.0-4.0)
6. **`leg_length_factor`**: Chiều dài chân (3.5-4.5)
7. **`elbow_outward_factor`**: Độ vươn ra của khuỷu tay (0.8-2.5)
8. **`wrist_outward_factor`**: Độ vươn ra của cổ tay (1.2-3.0)
9. **`limb_depth_factor`**: Độ sâu/độ dày chi (0.01-0.2)
10. **`knee_height_ratio`**: Tỷ lệ chiều cao đầu gối (0.3-0.7)

## Ảnh Hưởng Của Các Thông Số

- **Giảm `head_height_unit`** → Tăng distortion
- **Tăng `shoulder_width_factor`** → Tăng distortion
- **Tăng `elbow_outward_factor`, `wrist_outward_factor`** → Tăng distortion
- **Giảm `limb_depth_factor`** → Tăng distortion

## File Kết Quả

Các file JSON được tạo ra chứa:
- Thông số tối ưu
- Distortion coefficients đạt được
- Error cuối cùng
- Timestamp và metadata

Ví dụ: `manual_optimal_params_20250603_180257.json`

## Lưu Ý

1. **Dữ liệu ảnh**: Đặt ảnh người trong thư mục `human/` để sử dụng dữ liệu thực
2. **Camera matrix**: Được cố định trong quá trình tối ưu hóa
3. **Flags OpenCV**: Sử dụng `CALIB_USE_INTRINSIC_GUESS | CALIB_FIX_FOCAL_LENGTH | CALIB_FIX_PRINCIPAL_POINT`
4. **Số lượng ảnh**: Cần ít nhất 3-5 ảnh để hiệu chuẩn chính xác

## Mở Rộng

Để cải thiện thêm, có thể:
1. Thêm nhiều tổ hợp thông số vào `simple_optimizer.py`
2. Điều chỉnh bounds trong `parameter_optimizer.py`
3. Sử dụng thuật toán tối ưu hóa khác (PSO, GA, etc.)
4. Thêm constraints dựa trên kiến thức về tỷ lệ cơ thể người
